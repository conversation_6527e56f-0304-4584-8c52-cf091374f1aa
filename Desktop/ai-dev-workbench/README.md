# AI Dev Workbench (Electron + Next.js + Supabase)

All-in-one werkbank om AI-gestuurd SaaS/webapp automatisering te bouwen.
- Desktop: Electron wrapper
- UI: Next.js 14 (App Router)
- AI: Provider-agnostische OpenAI-compat client (GooseAI/Ollama/etc.)
- Agents: LangGraph flow (`codegen_flow`)
- Recipes: bv. `next-crud-module`
- Scheduler: BullMQ (Redis via Docker Compose)
- Tools: FS write, shell run, git commit
- Settings: API keys/providers
- Project Selector: kies lokale repo map (IPC stub aanwezig)

> Opmerking: jij koos **Electron** eerder. In deze starter is Electron opgenomen. Wil je later Tauri, dat kan in een aparte branch.

## Snelstart

```bash
pnpm i
pnpm dev    # start Next.js
pnpm desk   # start Electron (laadt http://localhost:3000)
```

## Services
- Redis via docker: `docker compose -f infra/docker/docker-compose.yml up -d`

## Env
Kopieer `.env.example` naar `apps/web/.env.local` en vul in.
