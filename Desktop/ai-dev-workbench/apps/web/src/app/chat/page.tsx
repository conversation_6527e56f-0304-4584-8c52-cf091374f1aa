'use client';
import { useState } from 'react';

export default function ChatPage() {
  const [prompt, setPrompt] = useState('Genereer een korte README');
  const [out, setOut] = useState('');
  const [model, setModel] = useState(process.env.NEXT_PUBLIC_DEFAULT_MODEL || 'gpt-j-6b');

  async function run() {
    const res = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ model, prompt })
    });
    const data = await res.json();
    setOut(data.text || 'No response');
  }

  return (
    <div className="max-w-3xl space-y-4">
      <h2 className="text-xl font-semibold">Chat</h2>
      <input className="w-full px-3 py-2 bg-slate-900 border border-slate-800 rounded"
             value={model} onChange={e=>setModel(e.target.value)} />
      <textarea className="w-full h-32 px-3 py-2 bg-slate-900 border border-slate-800 rounded"
        value={prompt} onChange={e=>setPrompt(e.target.value)} />
      <button onClick={run} className="px-4 py-2 bg-blue-600 rounded">Send</button>
      <pre className="bg-slate-900 border border-slate-800 p-3 rounded whitespace-pre-wrap">{out}</pre>
    </div>
  );
}
