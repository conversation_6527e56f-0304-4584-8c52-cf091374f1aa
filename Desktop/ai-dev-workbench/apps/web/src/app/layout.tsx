import "./globals.css";
import Link from "next/link";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="flex h-screen">
        <aside className="sidebar p-4 space-y-2">
          <h1 className="text-xl font-semibold mb-4">AI Dev Workbench</h1>
          <nav className="space-y-1">
            <Link className="link" href="/">Home</Link>
            <Link className="link" href="/chat">Chat</Link>
            <Link className="link" href="/recipes">Recipes</Link>
            <Link className="link" href="/scheduler">Scheduler</Link>
            <Link className="link" href="/extensions">Extensions</Link>
            <Link className="link" href="/settings">Settings</Link>
          </nav>
          <div className="mt-6 text-xs opacity-70">Project: <span id="project-path">not selected</span></div>
        </aside>
        <main className="flex-1 p-6 overflow-auto">{children}</main>
      </body>
    </html>
  );
}
