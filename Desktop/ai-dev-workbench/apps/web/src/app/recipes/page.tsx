'use client';
import { useState } from 'react';

export default function RecipesPage() {
  const [name, setName] = useState('customers');
  const [msg, setMsg] = useState('');

  async function run() {
    const res = await fetch('/api/recipes/next-crud-module', { method: 'POST', body: JSON.stringify({ name }) });
    const data = await res.json();
    setMsg(data.message || 'Done');
  }

  return (
    <div className="space-y-4 max-w-2xl">
      <h2 className="text-xl font-semibold">Recipes</h2>
      <div className="space-y-2 border border-slate-800 p-4 rounded bg-slate-900/50">
        <h3 className="font-semibold">Next CRUD Module</h3>
        <input className="w-full px-3 py-2 bg-slate-900 border border-slate-800 rounded"
               value={name} onChange={e=>setName(e.target.value)} />
        <button onClick={run} className="px-4 py-2 bg-green-600 rounded">Generate</button>
      </div>
      {msg && <pre className="bg-slate-900 border border-slate-800 p-3 rounded whitespace-pre-wrap">{msg}</pre>}
    </div>
  );
}
