'use client';
import { useState } from 'react';

export default function SchedulerPage() {
  const [status, setStatus] = useState('');
  async function schedule() {
    const res = await fetch('/api/scheduler/daily-refactor', { method: 'POST' });
    const data = await res.json();
    setStatus(JSON.stringify(data));
  }
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Scheduler</h2>
      <button onClick={schedule} className="px-4 py-2 bg-purple-600 rounded">Plan daily refactor</button>
      <pre className="bg-slate-900 border border-slate-800 p-3 rounded whitespace-pre-wrap">{status}</pre>
    </div>
  );
}
