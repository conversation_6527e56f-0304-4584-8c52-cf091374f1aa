export default function SettingsPage() {
  return (
    <div className="space-y-4 max-w-2xl">
      <h2 className="text-xl font-semibold">Settings</h2>
      <div className="space-y-2 border border-slate-800 p-4 rounded bg-slate-900/50">
        <h3 className="font-semibold">AI Provider</h3>
        <p>Configureer via environment variabelen (.env.local in apps/web).</p>
        <ul className="list-disc pl-5 text-sm opacity-80">
          <li>AI_PROVIDER (goose|local)</li>
          <li>AI_MODEL (bijv. gpt-j-6b)</li>
          <li>GOOSEAI_API_KEY</li>
          <li>OLLAMA_BASE_URL (optioneel)</li>
        </ul>
      </div>
      <div className="space-y-2 border border-slate-800 p-4 rounded bg-slate-900/50">
        <h3 className="font-semibold">Supabase</h3>
        <ul className="list-disc pl-5 text-sm opacity-80">
          <li>NEXT_PUBLIC_SUPABASE_URL</li>
          <li>NEXT_PUBLIC_SUPABASE_ANON_KEY</li>
        </ul>
      </div>
    </div>
  );
}
