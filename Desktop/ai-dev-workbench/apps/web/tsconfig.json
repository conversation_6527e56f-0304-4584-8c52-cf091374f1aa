{"compilerOptions": {"target": "ES2020", "module": "ESNext", "jsx": "preserve", "moduleResolution": "<PERSON><PERSON><PERSON>", "baseUrl": ".", "paths": {"@workbench/ai": ["../../packages/ai/src/index.ts"], "@workbench/recipes": ["../../packages/recipes/index.ts"], "@workbench/scheduler": ["../../packages/scheduler/index.ts"]}, "esModuleInterop": true, "strict": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "incremental": true, "resolveJsonModule": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["./src", ".next/types/**/*.ts"], "exclude": ["node_modules"]}