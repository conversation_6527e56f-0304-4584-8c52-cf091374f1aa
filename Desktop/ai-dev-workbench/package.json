{"name": "ai-dev-workbench", "private": true, "version": "0.1.0", "scripts": {"dev:web": "pnpm --filter @workbench/web dev", "dev:desktop": "pnpm --filter @workbench/desktop start", "dev": "concurrently -k \"pnpm dev:web\" \"pnpm dev:desktop\"", "desk": "pnpm --filter @workbench/desktop start", "build": "pnpm --filter @workbench/web build", "typecheck": "tsc -b", "lint": "eslint ."}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.5.4"}}