import OpenAI from "openai";

type Provider = "goose" | "local";

const map: Record<Provider, { baseURL: string; key: string | undefined }> = {
  goose: { baseURL: "https://api.goose.ai/v1", key: process.env.GOOSEAI_API_KEY },
  local: { baseURL: process.env.OLLAMA_BASE_URL || "http://localhost:11434/v1", key: "ollama" },
};

function client() {
  const prov = (process.env.AI_PROVIDER as Provider) || "goose";
  const { baseURL, key } = map[prov];
  return new OpenAI({ baseURL, apiKey: key });
}

export async function chat(model: string, prompt: string): Promise<string> {
  const c = client();
  const res = await c.chat.completions.create({
    model,
    messages: [{ role: "user", content: prompt }],
    temperature: 0.2,
  });
  return res.choices[0]?.message?.content ?? "";
}
