import { writeFileTree } from "@workbench/tools/fs";

export const nextCrudModule = {
  id: "next-crud-module",
  async run({ name }: { name: string }) {
    const target = process.cwd(); // in Electron kun je via IPC een gekozen pad doorgeven
    const files: Record<string,string> = {
      [`apps/web/src/app/${name}/page.tsx`]:
`export default function ${name[0].toUpperCase()+name.slice(1)}Page(){return <div>${name} page</div>}`,
      [`apps/web/src/app/api/${name}/route.ts`]:
`import { NextResponse } from 'next/server'; export async function GET(){return NextResponse.json([{id:1}])}`
    };
    await writeFileTree(target, files);
    return { ok: true, message: `CRUD module '${name}' aangemaakt in ${target}` };
  }
};
